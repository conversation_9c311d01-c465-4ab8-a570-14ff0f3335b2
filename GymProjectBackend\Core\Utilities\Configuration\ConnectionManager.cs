using Microsoft.Extensions.Configuration;
using System;
using System.IO;

namespace Core.Utilities.Configuration
{
    public static class ConnectionManager
    {
        private static string _connectionString;
        private static readonly object _lock = new object();

        public static string GetConnectionString()
        {
            if (string.IsNullOrEmpty(_connectionString))
            {
                lock (_lock)
                {
                    if (string.IsNullOrEmpty(_connectionString))
                    {
                        InitializeConnectionString();
                    }
                }
            }
            return _connectionString;
        }

        private static void InitializeConnectionString()
        {
            var configuration = new ConfigurationBuilder()
                .SetBasePath(Directory.GetCurrentDirectory())
                .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
                .Build();

            var environment = configuration["Environment"] ?? "dev";
            _connectionString = configuration[$"ConnectionStrings:{environment}"];

            if (string.IsNullOrEmpty(_connectionString))
            {
                throw new InvalidOperationException($"'{environment}' environment'ı için connection string bulunamadı!");
            }
        }
    }
}
